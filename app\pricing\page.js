"use client";

import Image from "next/image";
import Link from "next/link";

export default function Pricing() {
  const plans = [
    {
      name: "Personal Plan",
      price: "£9.99",
      period: "per month",
      description: "Perfect for individuals",
      features: [
        "Access to all basic courses",
        "Limited community access",
        "Email support response within 24 hours",
        "Up to 3 job applications per month"
      ],
      cta: "Start with Personal",
      popular: false,
      color: "indigo"
    },
    {
      name: "Expert Plan",
      price: "£29.99",
      period: "per month",
      description: "Best for professionals",
      features: [
        "Access to all courses including premium",
        "Full community access",
        "Priority email support",
        "Unlimited job applications",
        "Resume review (once per month)",
        "Career coaching session (monthly)",
        "Certification on course completion"
      ],
      cta: "Get Expert Plan",
      popular: true,
      color: "purple"
    },
    {
      name: "Enterprise Plan",
      price: "£79.99",
      period: "per month",
      description: "For teams and organizations",
      features: [
        "Everything in Expert plan",
        "Team management dashboard",
        "Dedicated account manager",
        "Custom learning paths",
        "API access",
        "Analytics and reporting",
        "Bulk enrollment discounts",
        "White-label options available",
        "SSO integration"
      ],
      cta: "Contact Sales",
      popular: false,
      color: "indigo"
    }
  ];

  const testimonials = [
    {
      id: 1,
      quote: "<PERSON>Mantra helped me land my dream job in tech within just 3 months of starting their courses.",
      author: "Sarah J.",
      role: "Software Developer",
      company: "Tech Innovations Inc.",
      avatar: "https://via.placeholder.com/48x48"
    },
    {
      id: 2,
      quote: "The expert plan was worth every penny. The career coaching sessions transformed my job search strategy completely.",
      author: "Michael T.",
      role: "Product Manager",
      company: "Growth Startup",
      avatar: "https://via.placeholder.com/48x48"
    },
    {
      id: 3,
      quote: "Our team's productivity increased by 30% after enrolling in JedMantra's enterprise courses.",
      author: "Lisa R.",
      role: "HR Director",
      company: "Global Solutions Ltd.",
      avatar: "https://via.placeholder.com/48x48"
    },
    {
      id: 4,
      quote: "The certification I earned through JedMantra was recognized immediately by employers in my industry.",
      author: "David K.",
      role: "Marketing Specialist",
      company: "Brand Leaders",
      avatar: "https://via.placeholder.com/48x48"
    }
  ];

  const faqs = [
    {
      question: "How are courses structured?",
      answer: "Our courses combine video lectures, interactive exercises, quizzes, and real-world projects. Most courses are self-paced, allowing you to learn on your own schedule."
    },
    {
      question: "Is Personal Plan sufficient for beginners?",
      answer: "Yes! The Personal Plan provides access to all our foundational courses, which are perfect for beginners. You can always upgrade later as your skills advance."
    },
    {
      question: "What happens when I upgrade or downgrade?",
      answer: "When you upgrade, you'll immediately gain access to additional features. If you downgrade, you'll retain access to your current plan until the end of your billing cycle."
    },
    {
      question: "How is Industry Role-Play™ different?",
      answer: "Industry Role-Play™ is our proprietary learning method that simulates real workplace scenarios. It's available in Expert and Enterprise plans, giving you practical experience that traditional courses don't offer."
    }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Custom CSS for animations */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
          }
          .animate-blob {
            animation: blob 7s infinite;
          }
          .animation-delay-2000 {
            animation-delay: 2s;
          }
          .animation-delay-4000 {
            animation-delay: 4s;
          }
        `
      }} />
      {/* Animated Background */}
      <div className="absolute inset-0 bg-white">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23E5E7EB' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      {/* Hero section */}
      <div className="relative z-10">
        <div className="max-w-7xl mx-auto py-20 px-4 sm:py-32 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 border border-purple-200 text-purple-800 text-sm font-medium mb-8">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
              Limited Time Offer - Save 30%
            </div>
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-gray-900 via-purple-800 to-blue-800 bg-clip-text text-transparent leading-tight">
              Choose Your Success Plan
            </h1>
            <p className="mt-8 max-w-2xl mx-auto text-xl text-gray-600 leading-relaxed">
              Unlock your potential with our premium learning platform. Join thousands of professionals who've transformed their careers.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center text-gray-600">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                14-day free trial
              </div>
              <div className="flex items-center text-gray-600">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                No credit card required
              </div>
              <div className="flex items-center text-gray-600">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Cancel anytime
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing section */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="text-center mb-16">
          {/* Enhanced Header with Glass Effect */}
          <div className="relative inline-block mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-3xl blur-xl"></div>
            <div className="relative bg-white/80 backdrop-blur-lg border border-white/30 rounded-3xl px-8 py-6 shadow-2xl">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-purple-800 to-blue-800 bg-clip-text text-transparent">
                  Transparent Pricing
                </h2>
              </div>
              <div className="w-16 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto mb-4"></div>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
                Choose the perfect plan for your learning journey. All plans include our core features with
                <span className="font-semibold text-purple-600"> no hidden fees</span> and
                <span className="font-semibold text-pink-600"> transparent pricing</span>.
              </p>

              {/* Feature Highlights */}
              <div className="flex flex-wrap justify-center gap-4 mt-6">
                <div className="flex items-center bg-green-100/80 backdrop-blur-sm border border-green-200/50 rounded-full px-4 py-2">
                  <svg className="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-800 text-sm font-medium">14-day free trial</span>
                </div>
                <div className="flex items-center bg-blue-100/80 backdrop-blur-sm border border-blue-200/50 rounded-full px-4 py-2">
                  <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-blue-800 text-sm font-medium">Cancel anytime</span>
                </div>
                <div className="flex items-center bg-purple-100/80 backdrop-blur-sm border border-purple-200/50 rounded-full px-4 py-2">
                  <svg className="w-4 h-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-purple-800 text-sm font-medium">Money-back guarantee</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-8">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative group ${
                plan.popular
                  ? "transform scale-105 z-10"
                  : "hover:scale-105"
              } transition-all duration-300`}
            >
              {/* Glass Card */}
              <div className="relative h-full rounded-3xl bg-white/80 backdrop-blur-lg border border-gray-200 shadow-2xl overflow-hidden">
                {/* Popular Badge */}
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                      ⭐ Most Popular
                    </div>
                  </div>
                )}

                {/* Gradient Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${
                  plan.popular
                    ? "from-purple-100/50 to-pink-100/50"
                    : "from-blue-50/30 to-indigo-50/30"
                } opacity-50`}></div>

                {/* Content */}
                <div className="relative z-10 p-8 h-full flex flex-col">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 ${
                      plan.popular
                        ? "bg-gradient-to-br from-purple-500 to-pink-500"
                        : "bg-gradient-to-br from-blue-500 to-indigo-500"
                    } shadow-lg`}>
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {plan.popular ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        ) : index === 0 ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        )}
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {plan.description}
                    </p>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-8">
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-gray-900">
                        {plan.price}
                      </span>
                      <span className="text-gray-600 ml-2 text-lg">
                        {plan.period}
                      </span>
                    </div>
                    {plan.popular && (
                      <div className="mt-2 text-green-600 text-sm font-medium">
                        Save 30% with annual billing
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="flex-1 mb-8">
                    <ul className="space-y-4">
                      {plan.features.map((feature, featureIdx) => (
                        <li key={featureIdx} className="flex items-start">
                          <div className="flex-shrink-0 mt-1">
                            <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                              plan.popular
                                ? "bg-gradient-to-r from-purple-500 to-pink-500"
                                : "bg-gradient-to-r from-blue-500 to-indigo-500"
                            }`}>
                              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                              </svg>
                            </div>
                          </div>
                          <p className="ml-3 text-gray-700 text-sm leading-relaxed">
                            {feature}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <div className="mt-auto">
                    <button
                      type="button"
                      className={`w-full py-4 px-6 rounded-2xl font-semibold text-white transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 ${
                        plan.popular
                          ? "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg shadow-purple-500/25 focus:ring-purple-500/25"
                          : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 shadow-lg shadow-gray-500/25 focus:ring-gray-500/25"
                      }`}
                    >
                      {plan.cta}
                    </button>
                    {!plan.popular && (
                      <p className="text-center text-gray-500 text-xs mt-3">
                        Upgrade or downgrade anytime
                      </p>
                    )}
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-4 right-4 w-20 h-20 bg-purple-100/30 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-16 h-16 bg-blue-100/30 rounded-full blur-lg"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-green-100 border border-green-200 text-green-800">
            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            30-day money-back guarantee on all plans
          </div>
        </div>
      </div>

      {/* Testimonials section */}
      <div className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Success Stories
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Join thousands of professionals who've transformed their careers with JedMantra
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="group relative"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Glass Card */}
                <div className="relative h-full rounded-2xl bg-white/90 backdrop-blur-lg border border-gray-200 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-transparent rounded-2xl"></div>

                  {/* Content */}
                  <div className="relative z-10">
                    {/* Quote Icon */}
                    <div className="mb-4">
                      <svg className="w-8 h-8 text-purple-500 opacity-60" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                      </svg>
                    </div>

                    {/* Quote */}
                    <p className="text-gray-700 text-sm leading-relaxed mb-6 italic">
                      "{testimonial.quote}"
                    </p>

                    {/* Author */}
                    <div className="flex items-center">
                      <div className="relative">
                        <Image
                          className="h-12 w-12 rounded-full object-cover ring-2 ring-white/20"
                          src={testimonial.avatar}
                          alt={testimonial.author}
                          width={48}
                          height={48}
                        />
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white/20"></div>
                      </div>
                      <div className="ml-4">
                        <h4 className="text-gray-900 font-semibold text-sm">
                          {testimonial.author}
                        </h4>
                        <p className="text-gray-600 text-xs">
                          {testimonial.role}
                        </p>
                        <p className="text-gray-500 text-xs">
                          {testimonial.company}
                        </p>
                      </div>
                    </div>

                    {/* Rating Stars */}
                    <div className="flex items-center mt-4 pt-4 border-t border-gray-200">
                      <div className="flex space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-gray-500 text-xs ml-2">5.0</span>
                    </div>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-2 right-2 w-16 h-16 bg-purple-100/30 rounded-full blur-xl"></div>
                </div>
              </div>
            ))}
          </div>

          {/* View More Button */}
          <div className="text-center mt-12">
            <button className="inline-flex items-center px-8 py-4 rounded-2xl bg-gray-100 border border-gray-200 text-gray-700 hover:bg-gray-200 transition-all duration-300 group">
              <span className="mr-2">View More Success Stories</span>
              <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Compare plans section */}
      <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-gray-900 text-center mb-12">
            Compare plans and features
          </h2>
          <div className="mt-12 space-y-4">
            <table className="min-w-full">
              <thead>
                <tr>
                  <th className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Feature
                  </th>
                  <th className="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Personal
                  </th>
                  <th className="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expert
                  </th>
                  <th className="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Enterprise
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {[
                  { feature: "Course access", personal: true, expert: true, enterprise: true },
                  { feature: "Community access", personal: "Limited", expert: "Full", enterprise: "Full" },
                  { feature: "Job applications", personal: "3/month", expert: "Unlimited", enterprise: "Unlimited" },
                  { feature: "Resume review", personal: false, expert: true, enterprise: true },
                  { feature: "Career coaching", personal: false, expert: true, enterprise: true },
                  { feature: "Team management", personal: false, expert: false, enterprise: true },
                  { feature: "API access", personal: false, expert: false, enterprise: true },
                  { feature: "White labeling", personal: false, expert: false, enterprise: true },
                  { feature: "SSO integration", personal: false, expert: false, enterprise: true },
                ].map((item, index) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : ""}>
                    <td className="py-4 px-6 text-sm font-medium text-gray-900">
                      {item.feature}
                    </td>
                    <td className="py-4 px-6 text-center">
                      {typeof item.personal === "boolean" ? (
                        item.personal ? (
                          <svg className="mx-auto h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <span className="inline-block h-5 w-5">—</span>
                        )
                      ) : (
                        <span className="text-sm text-gray-700">{item.personal}</span>
                      )}
                    </td>
                    <td className="py-4 px-6 text-center">
                      {typeof item.expert === "boolean" ? (
                        item.expert ? (
                          <svg className="mx-auto h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <span className="inline-block h-5 w-5">—</span>
                        )
                      ) : (
                        <span className="text-sm text-gray-700">{item.expert}</span>
                      )}
                    </td>
                    <td className="py-4 px-6 text-center">
                      {typeof item.enterprise === "boolean" ? (
                        item.enterprise ? (
                          <svg className="mx-auto h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <span className="inline-block h-5 w-5">—</span>
                        )
                      ) : (
                        <span className="text-sm text-gray-700">{item.enterprise}</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* FAQ section */}
      <div className="relative z-10 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our plans and features
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {faqs.map((faq, index) => (
              <div key={index} className="group">
                <div className="rounded-2xl bg-white/90 backdrop-blur-lg border border-gray-200 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="relative z-10">
                    <dt className="text-lg font-semibold text-gray-900 mb-3 flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-white text-sm font-bold">?</span>
                      </div>
                      {faq.question}
                    </dt>
                    <dd className="text-gray-700 leading-relaxed pl-9">
                      {faq.answer}
                    </dd>
                  </div>
                  <div className="absolute top-2 right-2 w-16 h-16 bg-purple-100/30 rounded-full blur-xl"></div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-4 rounded-2xl bg-gray-100 border border-gray-200 text-gray-700 hover:bg-gray-200 transition-all duration-300 group"
            >
              <span className="mr-2">Still have questions? Contact us</span>
              <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative z-10 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Glass CTA Card */}
          <div className="rounded-3xl bg-white/10 backdrop-blur-lg border border-white/20 shadow-2xl p-12 relative overflow-hidden">
            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20"></div>

            {/* Content */}
            <div className="relative z-10">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-500/20 border border-green-400/30 text-green-300 text-sm font-medium mb-6">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                14-Day Free Trial Available
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Ready to Transform
                <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Your Career?
                </span>
              </h2>

              <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto leading-relaxed">
                Join thousands of professionals who've accelerated their careers with JedMantra. Start your journey today with our risk-free trial.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link
                  href="/signup"
                  className="inline-flex items-center px-8 py-4 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25"
                >
                  <span className="mr-2">Start Free Trial</span>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>

                <Link
                  href="/demo"
                  className="inline-flex items-center px-8 py-4 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/30 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z" />
                  </svg>
                  Watch Demo
                </Link>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-6 justify-center items-center text-white/60 text-sm">
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No credit card required
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Cancel anytime
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  30-day money back
                </div>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute top-4 right-4 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-4 left-4 w-24 h-24 bg-pink-500/10 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Footer with logos */}
      <div className="relative z-10 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <p className="text-white/70 text-lg">
              Trusted by over 5000+ companies worldwide
            </p>
          </div>

          <div className="grid grid-cols-2 gap-8 md:grid-cols-4 lg:grid-cols-8 items-center">
            {["Google", "Microsoft", "Amazon", "IBM", "Cisco", "Meta", "Samsung", "Vimeo"].map((company, index) => (
              <div key={index} className="flex justify-center">
                <div className="rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 p-4 hover:bg-white/20 transition-all duration-300">
                  <Image
                    src={`https://via.placeholder.com/120x40/FFFFFF/9CA3AF?text=${company}`}
                    alt={company}
                    width={120}
                    height={40}
                    className="h-8 w-auto opacity-70 hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

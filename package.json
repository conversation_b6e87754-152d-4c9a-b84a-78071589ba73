{"name": "j<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "next": "^15.0.3", "react-hot-toast": "^2.4.1", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1", "uuid": "^11.1.0"}, "devDependencies": {"postcss": "^8", "tailwindcss": "^3.4.1"}}